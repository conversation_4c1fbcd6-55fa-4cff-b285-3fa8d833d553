name: Build and deploy Next.js app to Azure Web App - mmc

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js version
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'
          cache: 'npm'

      # Add Next.js build cache
      - name: Cache Next.js build
        uses: actions/cache@v4
        with:
          path: |
            ~/.npm
            ${{ github.workspace }}/.next/cache
          key: ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-${{ hashFiles('**/*.js', '**/*.jsx', '**/*.ts', '**/*.tsx') }}
          restore-keys: |
            ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-

      - name: npm install and build
        run: |
          npm install --force
          npm run build
        env:
          CI: true
          NEXT_PUBLIC_API_URL: ${{ secrets.NEXT_PUBLIC_API_URL }}

      - name: Prepare deployment package
        run: |
          # Create optimized deployment package for Azure
          mkdir deployment
          # Copy build output and necessary files
          cp -r .next deployment/
          cp -r public deployment/
          cp package.json deployment/
          cp package-lock.json deployment/
          cp next.config.mjs deployment/
          
          # Create a server.js file for Azure App Service
          echo 'const { createServer } = require("http");
          const { parse } = require("url");
          const next = require("next");
          
          // Make sure environment variables are properly set
          console.log("API URL:", process.env.NEXT_PUBLIC_API_URL || "Not set");
          
          const dev = process.env.NODE_ENV !== "production";
          const hostname = "0.0.0.0";
          const port = process.env.PORT || 8080;
          
          const app = next({ dev, hostname, port });
          const handle = app.getRequestHandler();
          
          app.prepare().then(() => {
            createServer(async (req, res) => {
              try {
                const parsedUrl = parse(req.url, true);
                await handle(req, res, parsedUrl);
              } catch (err) {
                console.error("Error occurred handling", req.url, err);
                res.statusCode = 500;
                res.end("Internal Server Error");
              }
            })
              .once("error", (err) => {
                console.error(err);
                process.exit(1);
              })
              .listen(port, () => {
                console.log(`> Ready on http://${hostname}:${port}`);
              });
          });' > deployment/server.js
          
          # Create web.config for IIS
          echo '<?xml version="1.0" encoding="utf-8"?>
          <configuration>
            <system.webServer>
              <webSocket enabled="false" />
              <handlers>
                <add name="iisnode" path="server.js" verb="*" modules="iisnode"/>
              </handlers>
              <rewrite>
                <rules>
                  <rule name="NodeInspector" patternSyntax="ECMAScript" stopProcessing="true">
                    <match url="^server.js\/debug[\/]?" />
                  </rule>
                  <rule name="StaticContent">
                    <action type="Rewrite" url="public{REQUEST_URI}"/>
                  </rule>
                  <rule name="DynamicContent">
                    <conditions>
                      <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="True"/>
                    </conditions>
                    <action type="Rewrite" url="server.js"/>
                  </rule>
                </rules>
              </rewrite>
              <security>
                <requestFiltering removeServerHeader="true">
                  <hiddenSegments>
                    <remove segment="bin"/>
                  </hiddenSegments>
                </requestFiltering>
              </security>
              <httpErrors existingResponse="PassThrough" />
            </system.webServer>
          </configuration>' > deployment/web.config
          
          # Create a production startup script
          echo '{
            "scripts": {
              "start": "node server.js"
            }
          }' > deployment/package.json.temp
          
          # Merge with existing package.json
          jq -s '.[0] * .[1]' deployment/package.json deployment/package.json.temp > deployment/package.json.new
          mv deployment/package.json.new deployment/package.json
          rm deployment/package.json.temp
          
          # Create zip for deployment
          cd deployment
          zip -r ../release.zip .
          cd ..

      - name: Upload artifact for deployment job
        uses: actions/upload-artifact@v4
        with:
          name: node-app
          path: release.zip

  deploy:
    runs-on: ubuntu-latest
    needs: build
    environment:
      name: 'Production'
      url: ${{ steps.deploy-to-webapp.outputs.webapp-url }}
    permissions:
      id-token: write
      contents: read

    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: node-app

      - name: Login to Azure
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZUREAPPSERVICE_CLIENTID_766A6DE453C246DDB52F7DDB24B58EA9 }}
          tenant-id: ${{ secrets.AZUREAPPSERVICE_TENANTID_3D88489CDA6643C5B038115FD8A3AC6D }}
          subscription-id: ${{ secrets.AZUREAPPSERVICE_SUBSCRIPTIONID_45F309ED906F45E191AF1B7E90884158 }}

      - name: 'Deploy to Azure Web App'
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: 'mmc'
          slot-name: 'Production'
          package: release.zip
